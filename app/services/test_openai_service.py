import pytest
import io
import json
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import UploadFile, HTTPException
from app.services.openai_service import extract_from_pdf, client

# 模拟OpenAI响应的辅助函数
def create_mock_response(data):
    """创建一个模拟的OpenAI API响应对象"""
    mock_model = MagicMock()
    mock_model.model_dump.return_value = data
    
    mock_response = MagicMock()
    mock_response.output_parsed = mock_model
    return mock_response

@pytest.mark.asyncio
async def test_extract_from_pdf_success():
    """
    测试extract_from_pdf函数的正向流程
    使用assets文件夹中的真实文件，并模拟OpenAI API调用
    """
    # 准备测试文件
    pdf_path = "assets/aaa.pdf"
    image_path = "assets/google.png"
    
    # 尝试读取文件
    try:
        with open(pdf_path, "rb") as pdf_file, open(image_path, "rb") as image_file:
            pdf_content = pdf_file.read()
            image_content = image_file.read()
    except FileNotFoundError as e:
        pytest.skip(f"测试文件不存在: {e}. 请确保测试文件已放置在assets文件夹中。")
    
    # 创建UploadFile对象
    pdf = UploadFile(
        filename="aaa.pdf",
        file=io.BytesIO(pdf_content),
        content_type="application/pdf"
    )
    
    image = UploadFile(
        filename="google.png",
        file=io.BytesIO(image_content),
        content_type="image/png"
    )
    
    # 按照要求准备提取字段
    fields_to_extract = {
        "Tenant's Full name": "位于文档第一页的中间位置，在TENANT DETAIL表格中",
        "Tenant's Current Address": "位于文档第一页的中间位置，在TENANT DETAIL表格中"
    }
    fields_to_extract_str = json.dumps(fields_to_extract)
    
    # 模拟的API响应数据
    expected_data = {
        "Tenant's Full name": "John Doe",
        "Tenant's Current Address": "123 Example Street, City, Country"
    }
    
    # 创建模拟的API响应
    mock_response = create_mock_response(expected_data)
    
    # 模拟OpenAI API调用
    with patch.object(client.responses, 'parse', new_callable=AsyncMock) as mock_parse:
        # 设置模拟响应
        mock_parse.return_value = mock_response
        
        # 调用被测函数
        result = await extract_from_pdf([pdf, image], fields_to_extract_str)
        
        # 验证结果
        assert result == expected_data
        
        # 验证API调用
        mock_parse.assert_called_once()
        
        # 验证API调用参数
        call_kwargs = mock_parse.call_args.kwargs
        assert call_kwargs['model'] == 'gpt-4o'
        assert call_kwargs['temperature'] == 0
        
        # 验证请求内容
        request_input = call_kwargs['input']
        assert len(request_input) == 2
        
        # 验证系统消息
        system_message = request_input[0]
        assert system_message['role'] == 'system'
        assert '专家' in system_message['content']
        
        # 验证用户消息内容
        user_message = request_input[1]
        assert user_message['role'] == 'user'
        user_content = user_message['content']
        
        # 验证用户内容包含PDF、图片和文本提示
        assert len(user_content) == 3
        assert any(item['type'] == 'input_file' for item in user_content)
        assert any(item['type'] == 'input_image' for item in user_content)
        assert any(item['type'] == 'input_text' for item in user_content)
        
        # 验证提示文本内容包含所有字段
        prompt_item = next(item for item in user_content if item['type'] == 'input_text')
        prompt_text = prompt_item['text']
        assert "请从以上提供的所有文件中提取以下字段" in prompt_text
        
        # 验证字段在提示中
        for field_name in fields_to_extract:
            assert field_name in prompt_text 