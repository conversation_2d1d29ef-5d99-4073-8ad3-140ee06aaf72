import pytest
import io
import json
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import UploadFile, HTTPException
from app.services.openai_service import extract_from_pdf, client


# 模拟OpenAI响应的辅助函数
def create_mock_response(data):
    """创建一个模拟的OpenAI API响应对象"""
    mock_model = MagicMock()
    mock_model.model_dump.return_value = data
    
    mock_response = MagicMock()
    mock_response.output_parsed = mock_model
    return mock_response


@pytest.mark.asyncio
async def test_extract_from_pdf_success_with_pdf_and_image():
    """
    测试extract_from_pdf函数的正向流程
    使用assets文件夹中的真实PDF和图片文件，并模拟OpenAI API调用
    """
    # 准备测试文件
    pdf_path = "assets/aaa.pdf"
    image_path = "assets/google.png"
    
    # 尝试读取文件
    try:
        with open(pdf_path, "rb") as pdf_file, open(image_path, "rb") as image_file:
            pdf_content = pdf_file.read()
            image_content = image_file.read()
    except FileNotFoundError as e:
        pytest.skip(f"测试文件不存在: {e}. 请确保测试文件已放置在assets文件夹中。")
    
    # 创建UploadFile对象
    pdf = UploadFile(
        filename="aaa.pdf",
        file=io.BytesIO(pdf_content),
        content_type="application/pdf"
    )
    
    image = UploadFile(
        filename="google.png",
        file=io.BytesIO(image_content),
        content_type="image/png"
    )
    
    # 准备提取字段
    fields_to_extract = {
        "company_name": "公司名称，通常在文档顶部",
        "total_amount": "总金额，通常在文档底部"
    }
    fields_to_extract_str = json.dumps(fields_to_extract)
    
    # 模拟的API响应数据
    expected_data = {
        "company_name": "Google Inc.",
        "total_amount": "$1,000.00"
    }
    
    # 创建模拟的API响应
    mock_response = create_mock_response(expected_data)
    
    # 模拟OpenAI API调用
    with patch.object(client.responses, 'parse', new_callable=AsyncMock) as mock_parse:
        # 设置模拟响应
        mock_parse.return_value = mock_response
        
        # 调用被测函数
        result = await extract_from_pdf([pdf, image], fields_to_extract_str)
        
        # 验证结果
        assert result == expected_data
        
        # 验证API调用
        mock_parse.assert_called_once()
        
        # 验证API调用参数
        call_kwargs = mock_parse.call_args.kwargs
        assert call_kwargs['model'] == 'gpt-4o'
        assert call_kwargs['temperature'] == 0
        
        # 验证请求内容
        request_input = call_kwargs['input']
        assert len(request_input) == 2
        
        # 验证系统消息
        system_message = request_input[0]
        assert system_message['role'] == 'system'
        assert '专家' in system_message['content']
        
        # 验证用户消息内容
        user_message = request_input[1]
        assert user_message['role'] == 'user'
        user_content = user_message['content']
        
        # 验证用户内容包含PDF、图片和文本提示
        assert len(user_content) == 3
        assert any(item['type'] == 'input_file' for item in user_content)
        assert any(item['type'] == 'input_image' for item in user_content)
        assert any(item['type'] == 'input_text' for item in user_content)


@pytest.mark.asyncio
async def test_extract_from_pdf_single_pdf():
    """测试只使用单个PDF文件的情况"""
    pdf_path = "assets/aaa.pdf"
    
    try:
        with open(pdf_path, "rb") as pdf_file:
            pdf_content = pdf_file.read()
    except FileNotFoundError:
        pytest.skip("测试PDF文件不存在")
    
    pdf = UploadFile(
        filename="aaa.pdf",
        file=io.BytesIO(pdf_content),
        content_type="application/pdf"
    )
    
    fields_to_extract_str = json.dumps({"invoice_number": "发票号码"})
    expected_data = {"invoice_number": "INV-001"}
    mock_response = create_mock_response(expected_data)
    
    with patch.object(client.responses, 'parse', new_callable=AsyncMock) as mock_parse:
        mock_parse.return_value = mock_response
        
        result = await extract_from_pdf([pdf], fields_to_extract_str)
        
        assert result == expected_data
        mock_parse.assert_called_once()


@pytest.mark.asyncio
async def test_extract_from_pdf_single_image():
    """测试只使用单个图片文件的情况"""
    image_path = "assets/google.png"
    
    try:
        with open(image_path, "rb") as image_file:
            image_content = image_file.read()
    except FileNotFoundError:
        pytest.skip("测试图片文件不存在")
    
    image = UploadFile(
        filename="google.png",
        file=io.BytesIO(image_content),
        content_type="image/png"
    )
    
    fields_to_extract_str = json.dumps({"logo_text": "图片中的文字"})
    expected_data = {"logo_text": "Google"}
    mock_response = create_mock_response(expected_data)
    
    with patch.object(client.responses, 'parse', new_callable=AsyncMock) as mock_parse:
        mock_parse.return_value = mock_response
        
        result = await extract_from_pdf([image], fields_to_extract_str)
        
        assert result == expected_data
        mock_parse.assert_called_once()


@pytest.mark.asyncio
async def test_extract_from_pdf_empty_files():
    """测试空文件列表的异常情况"""
    fields_to_extract_str = json.dumps({"field": "description"})
    
    with pytest.raises(HTTPException) as exc_info:
        await extract_from_pdf([], fields_to_extract_str)
    
    assert exc_info.value.status_code == 400
    assert "没有提供任何文件" in exc_info.value.detail


@pytest.mark.asyncio
async def test_extract_from_pdf_invalid_json():
    """测试无效JSON字符串的异常情况"""
    # 创建一个虚拟的UploadFile
    dummy_file = UploadFile(
        filename="test.pdf",
        file=io.BytesIO(b"dummy content"),
        content_type="application/pdf"
    )
    
    invalid_json = "invalid json string"
    
    with pytest.raises(HTTPException) as exc_info:
        await extract_from_pdf([dummy_file], invalid_json)
    
    assert exc_info.value.status_code == 400
    assert "JSON无效" in exc_info.value.detail


@pytest.mark.asyncio
async def test_extract_from_pdf_non_dict_json():
    """测试JSON不是字典的异常情况"""
    dummy_file = UploadFile(
        filename="test.pdf",
        file=io.BytesIO(b"dummy content"),
        content_type="application/pdf"
    )
    
    non_dict_json = json.dumps(["not", "a", "dict"])
    
    with pytest.raises(HTTPException) as exc_info:
        await extract_from_pdf([dummy_file], non_dict_json)
    
    assert exc_info.value.status_code == 400
    assert "JSON无效" in exc_info.value.detail


@pytest.mark.asyncio
async def test_extract_from_pdf_unsupported_file_type():
    """测试不支持的文件类型"""
    unsupported_file = UploadFile(
        filename="test.txt",
        file=io.BytesIO(b"text content"),
        content_type="text/plain"
    )
    
    fields_to_extract_str = json.dumps({"field": "description"})
    
    with pytest.raises(HTTPException) as exc_info:
        await extract_from_pdf([unsupported_file], fields_to_extract_str)
    
    assert exc_info.value.status_code == 400
    assert "提供的所有文件类型均不被支持" in exc_info.value.detail


@pytest.mark.asyncio
async def test_extract_from_pdf_openai_api_error():
    """测试OpenAI API调用失败的情况"""
    dummy_file = UploadFile(
        filename="test.pdf",
        file=io.BytesIO(b"dummy content"),
        content_type="application/pdf"
    )
    
    fields_to_extract_str = json.dumps({"field": "description"})
    
    with patch.object(client.responses, 'parse', new_callable=AsyncMock) as mock_parse:
        # 模拟API调用失败
        mock_parse.side_effect = Exception("API调用失败")
        
        with pytest.raises(HTTPException) as exc_info:
            await extract_from_pdf([dummy_file], fields_to_extract_str)
        
        assert exc_info.value.status_code == 500
        assert "OpenAI API调用时发生内部错误" in exc_info.value.detail


@pytest.mark.asyncio
async def test_extract_from_pdf_file_without_content_type():
    """测试文件没有content_type的情况"""
    file_without_type = UploadFile(
        filename="test.pdf",
        file=io.BytesIO(b"dummy content"),
        content_type=None
    )
    
    fields_to_extract_str = json.dumps({"field": "description"})
    
    with pytest.raises(HTTPException) as exc_info:
        await extract_from_pdf([file_without_type], fields_to_extract_str)
    
    assert exc_info.value.status_code == 400
    assert "提供的所有文件类型均不被支持" in exc_info.value.detail
