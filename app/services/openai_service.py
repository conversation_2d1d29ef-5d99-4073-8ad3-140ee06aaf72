import base64
import json
from typing import Dict, Any, Type, List

from openai import OpenAI
from pydantic import BaseModel, create_model
from fastapi import UploadFile, HTTPException
from loguru import logger

OPENAI_API_KEY = " ********************************************************************************************************************************************************************"

client = OpenAI(api_key=OPENAI_API_KEY)


def _create_dynamic_extraction_model(fields: Dict[str, Any]) -> Type[BaseModel]:
    """根据字段字典动态创建一个Pydantic模型。"""
    field_definitions = {field_name: (str, ...) for field_name in fields.keys()}
    return create_model('DynamicExtractionModel', **field_definitions)


async def _process_uploaded_file(file: UploadFile) -> Dict[str, Any] | None:
    """
    处理单个上传的文件，将其转换为OpenAI API所需的格式。
    返回一个字典或在不支持的文件类型下返回None。
    """
    mime_type = file.content_type
    if not mime_type:
        logger.warning(f"无法确定文件 {file.filename} 的MIME类型，已跳过。")
        return None

    file_bytes = await file.read()
    base64_file = base64.b64encode(file_bytes).decode('utf-8')
    file_data = f"data:{mime_type};base64,{base64_file}"

    if mime_type == "application/pdf":
        logger.info(f"PDF文件 {file.filename} 已处理。")
        return {"type": "input_file", "filename": file.filename, "file_data": file_data}
    
    if mime_type.startswith("image/"):
        logger.info(f"图片文件 {file.filename} 已处理。")
        return {"type": "input_image", "image_url": file_data}

    logger.warning(f"不支持的文件类型: {mime_type} - {file.filename}。已跳过。")
    return None


async def extract_from_pdf(files: List[UploadFile], fields_to_extract_str: str) -> Dict[str, Any]:
    """
    使用OpenAI的视觉和结构化输出功能，从多个文件（PDF或图片）中提取结构化数据。
    """
    if not files:
        raise HTTPException(status_code=400, detail="没有提供任何文件。")

    try:
        fields_to_extract = json.loads(fields_to_extract_str)
        if not isinstance(fields_to_extract, dict):
            raise ValueError("要提取的字段必须是JSON对象。")
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"字段提取指令JSON解析失败: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"用于要提取字段的JSON无效: {e}")

    ExtractionModel = _create_dynamic_extraction_model(fields_to_extract)
    
    logger.info(f"开始处理 {len(files)} 个上传的文件。")
    processed_files = [await _process_uploaded_file(file) for file in files]
    user_content = [item for item in processed_files if item]

    if not user_content:
         raise HTTPException(status_code=400, detail="提供的所有文件类型均不被支持。")
         
    prompt_text = (
        "请从以上提供的所有文件中提取以下字段。"
        "请综合所有文档的信息来回答。"
        "以下是字段及其查找提示：\n\n"
        f"{json.dumps(fields_to_extract, indent=2, ensure_ascii=False)}"
    )
    user_content.append({"type": "input_text", "text": prompt_text})

    system_message = (
        "您是一位从多个文档（可能包含PDF和图片）中提取结构化数据的专家。"
        "您将收到一系列文件和一个描述要提取数据的文本提示。"
    )
    
    logger.info("所有文件处理完毕，准备调用OpenAI API。")
    
    try:
        response = client.responses.parse(
            model="gpt-4o",
            temperature=0,
            input=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_content},
            ],
            text_format=ExtractionModel,
        )

        logger.success("成功从OpenAI收到响应。")
        return response.output_parsed.model_dump()

    except Exception as e:
        logger.exception("--- ERROR: OpenAI API 调用失败 ---")
        raise HTTPException(status_code=500, detail=f"OpenAI API调用时发生内部错误: {str(e)}") 